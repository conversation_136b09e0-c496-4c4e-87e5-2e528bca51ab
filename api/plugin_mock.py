"""
插件API模拟器
当插件功能被禁用时，返回Dify期望的PluginDaemonBasicResponse格式
"""

from flask import Flask, jsonify
from flask_cors import CORS
import threading
import time

app = Flask(__name__)
CORS(app)

@app.route('/plugin/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def mock_plugin_api(path):
    """模拟插件API，返回Dify期望的PluginDaemonBasicResponse格式"""
    if 'tasks' in path:
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': [],
            'total': 0,
            'page': 1,
            'page_size': 100
        })
    elif 'models' in path:
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': [],
            'total': 0
        })
    else:
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': None
        })

def run_mock_server():
    """在后台运行模拟服务器"""
    app.run(host='127.0.0.1', port=9999, debug=False)

if __name__ == '__main__':
    # 启动模拟服务器
    print("🔌 启动插件API模拟器在端口9999...")
    run_mock_server()
