"""
修复的插件API模拟器
严格按照Dify的PluginDaemonBasicResponse格式返回数据
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import threading
import time

app = Flask(__name__)
CORS(app)

@app.route('/plugin/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def mock_plugin_api(path):
    """模拟插件API，返回严格的PluginDaemonBasicResponse格式"""
    
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 100, type=int)
    
    # 根据路径返回不同的数据结构
    if 'tasks' in path:
        # 对于任务列表，data应该是一个列表
        response_data = {
            'code': 200,
            'message': 'success',
            'data': []  # 空的任务列表
        }
    elif 'models' in path:
        # 对于模型列表，data应该是一个列表
        response_data = {
            'code': 200,
            'message': 'success', 
            'data': []  # 空的模型列表
        }
    elif 'list' in path:
        # 对于插件列表
        response_data = {
            'code': 200,
            'message': 'success',
            'data': []  # 空的插件列表
        }
    elif 'debugging-key' in path:
        # 对于调试密钥
        response_data = {
            'code': 200,
            'message': 'success',
            'data': None
        }
    else:
        # 默认响应
        response_data = {
            'code': 200,
            'message': 'success',
            'data': None
        }
    
    return jsonify(response_data)

@app.route('/', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'ok',
        'service': 'plugin-mock',
        'message': 'Plugin mock service is running'
    })

def run_mock_server():
    """在后台运行模拟服务器"""
    app.run(host='127.0.0.1', port=9999, debug=False)

if __name__ == '__main__':
    # 启动模拟服务器
    print("🔌 启动修复的插件API模拟器在端口9999...")
    run_mock_server()
